import { useState } from "react";
import { useNavigate } from "react-router-dom";
import api from "../services/api/api";
import useAuthStore from "../store/useAuthStore";
import useUserDataStore from "../store/useUserDataStore";
import { showErrorToast } from "../utils/showErrorToast";
import { showSuccessToast } from "../utils/showSuccessToast";
import { FaEye, FaEyeSlash } from "react-icons/fa";

export default function Login() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const login = useAuthStore((state) => state.login);
  const { fetchUserData } = useUserDataStore();
  const [showPassword, setShowPassword] = useState(false);

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const { data, status } = await api.post("/auth/login", { email, password });

      if (status === 201) {
        login(data.user, data.access_token);
        // showSuccessToast("Login successful!");
        navigate("/home");
      }
    } catch (error) {
      console.log(error,'error');

      showErrorToast(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="h-screen flex items-center justify-center">
      <form className="bg-darkCard p-6 rounded-lg shadow-lg" onSubmit={handleLogin}>
        <h2 className="text-2xl text-neonBlue font-bold mb-4">Login</h2>

        <input
          type="email"
          className="w-full p-2 mb-3 rounded bg-gray-800 text-white"
          placeholder="Email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
        />

<div className="relative mb-3">
  <input
    type={showPassword ? "text" : "password"}
    className="w-full p-2 pr-10 rounded bg-gray-800 text-white"
    placeholder="Password"
    value={password}
    onChange={(e) => setPassword(e.target.value)}
    required
  />
  <span
    className="absolute right-3 top-3 cursor-pointer text-gray-400"
    onClick={() => setShowPassword((prev) => !prev)}
  >
    {showPassword ? <FaEyeSlash /> : <FaEye />}
  </span>
</div>


        <button
          type="submit"
          className="w-full bg-neonGreen p-2 rounded"
          disabled={loading}
        >
          {loading ? "Logging in..." : "Login"}
        </button>

        <p className="mt-2 text-sm text-gray-400">
          Forgot password? <a href="/forgot-password" className="text-neonBlue">Reset it</a>
        </p>

        <p className="mt-2 text-sm text-gray-400">
          No account? <a href="/register" className="text-neonBlue">Sign up</a>
        </p>
      </form>
    </div>
  );
}
