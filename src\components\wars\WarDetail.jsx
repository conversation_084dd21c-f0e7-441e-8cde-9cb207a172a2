import React, { useState, useEffect } from 'react';
import { useParams, Link } from 'react-router-dom';
import { warService } from '../../services/api/war.service';
import { showSuccessToast } from '../../utils/showSuccessToast';
import { showErrorToast } from '../../utils/showErrorToast';
import useUserDataStore from '../../store/useUserDataStore';
import { formatDate } from '../../utils/formatDate';
// Hidden tabs - imports commented out
// import WarPreparation from './WarPreparation';
// import WarSupplies from './WarSupplies';
// import WarMorale from './WarMorale';
// import WarCosts from './WarCosts';
// import WarEvents from './WarEvents';
import WarParticipateForm from './WarParticipateForm';
import RegionWarHistory from '../analytics/RegionWarHistory';
import RegionalPerformance from '../analytics/RegionalPerformance';

const WarDetail = () => {
  const { id } = useParams();
  const [war, setWar] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [participating, setParticipating] = useState(false);
  const [participationLoading, setParticipationLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const { userData, fetchUserData } = useUserDataStore();

  // Modal state
  const [showModal, setShowModal] = useState(false);
  const [modalSide, setModalSide] = useState(null); // 'attackers' or 'defenders'

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Create an array of promises to execute in parallel
        const [warData, myWars] = await Promise.all([
          // Fetch war data
          warService.getWar(id),
          // Check if user is already participating
          warService.getMyWars()
        ]);

        // Set war data
        setWar(warData);

        // Check participation
        const isParticipating = myWars.some(w => w.id === warData.id);
        setParticipating(isParticipating);

        setError(null);
      } catch (err) {
        console.error('Error fetching war details:', err);
        setError('Failed to load war details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id]);

  const handleJoinWar = async (isAttacker) => {
    if (participating) return;

    try {
      setParticipationLoading(true);

      // Refresh user data to get the latest energy amount
      await fetchUserData(true);

      // Get the latest energy value
      const latestEnergy = userData?.energy || 0;

      if (latestEnergy <= 0) {
        showErrorToast('You do not have enough energy to participate in the war.');
        return;
      }

      // Show success message
      showSuccessToast(`Successfully joined the war as ${isAttacker ? 'attacker' : 'defender'}!`);

      // Refresh user data to update energy display
      await fetchUserData(true);

      // Then refresh war data
      const updatedWar = await warService.getWar(id);
      setWar(updatedWar);
      setParticipating(true);

      // Set active tab to participate after joining
      setActiveTab('participate');
    } catch (err) {
      console.error('Error joining war:', err);
      showErrorToast(err);
      setError('Failed to join the war. Please try again later.');
    } finally {
      setParticipationLoading(false);
    }
  };

  const handleParticipationComplete = async () => {
    // Refresh war data after participation
    try {
      const updatedWar = await warService.getWar(id);
      setWar(updatedWar);

      // Also refresh user data to update energy display in the UI
      await fetchUserData(true);
    } catch (err) {
      console.error('Error refreshing war data:', err);
      showErrorToast('Failed to refresh war data');
    }
  };

  if (loading) return <div className="flex justify-center mt-8"><div className="loader">Loading...</div></div>;
  if (error) return <div className="text-red-500 mt-4">{error}</div>;
  if (!war) return <div>War not found</div>;

  const attackers = war.participants?.attackers || [];
  const defenders = war.participants?.defenders || [];

  const getProgressPercentage = () => {
    const attackerDamage = (war.attackerGroundDamage || 0) + (war.attackerSeaDamage || 0);
    const defenderDamage = (war.defenderGroundDamage || 0) + (war.defenderSeaDamage || 0) + (war.damageRequirement || 0);
    const total = attackerDamage + defenderDamage || 1;
    return (attackerDamage / total) * 100;
  };

  // Helper function to check if a war is in an active state
  const isWarActive = (status) => {
    const activeStatuses = ['ground_phase'];
    return activeStatuses.includes(status);
  };

  // Helper function to consolidate participants and sum up damage
  const consolidateParticipants = (participants) => {
    const consolidated = {};

    participants.forEach(participant => {
      const key = participant.userId
        ? `user-${participant.userId}`
        : participant.state
          ? `state-${participant.state.id}`
          : `unknown-${participant.id}`;

      if (!consolidated[key]) {
        consolidated[key] = {
          ...participant,
          totalDamage: participant.damage || 0,
          count: 1
        };
      } else {
        consolidated[key].totalDamage += (participant.damage || 0);
        consolidated[key].count += 1;
      }
    });

    return Object.values(consolidated).sort((a, b) => b.totalDamage - a.totalDamage);
  };

  // Process participants data
  const consolidatedAttackers = attackers ? consolidateParticipants(attackers) : [];
  const consolidatedDefenders = defenders ? consolidateParticipants(defenders) : [];
  
  return (
    <div className="container mx-auto p-4">
      <div className="mb-6 flex justify-between items-center">
        <Link to="/wars" className="text-blue-600 hover:text-blue-800">
          ← Back to Wars
        </Link>
      </div>

      <div className="bg-gray-800 rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold text-white">{war.warType.toUpperCase()} War</h1>
        </div>

        {/* Tabs Navigation */}
        <div className="mb-6 border-b border-gray-700 overflow-x-auto">
          <nav className="flex whitespace-nowrap space-x-1">
            <button
              onClick={() => setActiveTab('overview')}
              className={`py-2 px-4 ${activeTab === 'overview' ? 'text-neonBlue border-b-2 border-neonBlue' : 'text-gray-400 hover:text-gray-300'}`}
            >
              Overview
            </button>
            {participating && isWarActive(war.status) && (
              <button
                onClick={() => setActiveTab('participate')}
                className={`py-2 px-4 ${activeTab === 'participate' ? 'text-neonBlue border-b-2 border-neonBlue' : 'text-gray-400 hover:text-gray-300'}`}
              >
                Fight
              </button>
            )}
            {/* <button
              onClick={() => setActiveTab('preparation')}
              className={`py-2 px-4 ${activeTab === 'preparation' ? 'text-neonBlue border-b-2 border-neonBlue' : 'text-gray-400 hover:text-gray-300'}`}
            >
              Participate
            </button> */}
            {/* <button
              onClick={() => setActiveTab('supplies')}
              className={`py-2 px-4 ${activeTab === 'supplies' ? 'text-neonBlue border-b-2 border-neonBlue' : 'text-gray-400 hover:text-gray-300'}`}
            >
              Supplies
            </button>
            <button
              onClick={() => setActiveTab('morale')}
              className={`py-2 px-4 ${activeTab === 'morale' ? 'text-neonBlue border-b-2 border-neonBlue' : 'text-gray-400 hover:text-gray-300'}`}
            >
              Morale
            </button>
            <button
              onClick={() => setActiveTab('costs')}
              className={`py-2 px-4 ${activeTab === 'costs' ? 'text-neonBlue border-b-2 border-neonBlue' : 'text-gray-400 hover:text-gray-300'}`}
            >
              Costs
            </button>
            <button
              onClick={() => setActiveTab('events')}
              className={`py-2 px-4 ${activeTab === 'events' ? 'text-neonBlue border-b-2 border-neonBlue' : 'text-gray-400 hover:text-gray-300'}`}
            >
              Events
            </button> */}
            <button
              onClick={() => setActiveTab('history')}
              className={`py-2 px-4 ${activeTab === 'history' ? 'text-neonBlue border-b-2 border-neonBlue' : 'text-gray-400 hover:text-gray-300'}`}
            >
              Region History
            </button>
            <button
              onClick={() => setActiveTab('performance')}
              className={`py-2 px-4 ${activeTab === 'performance' ? 'text-neonBlue border-b-2 border-neonBlue' : 'text-gray-400 hover:text-gray-300'}`}
            >
              Performance
            </button>
          </nav>
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div>
            <div className="mb-6">
              <p className="mb-2 text-gray-300">
                <span className="font-medium text-white">Target:</span> {war.warTarget}
              </p>

              {war.region && (
                <p className="mb-2 text-gray-300">
                  <span className="font-medium text-white">Target Region:</span>{' '}
                  <Link to={`/regions/${war.region.id}`} className="text-neonBlue hover:text-blue-400">
                    {war.region.name}
                  </Link>
                </p>
              )}
              {war.state && (
                <p className="mb-2 text-gray-300">
                  <span className="font-medium text-white">Target State:</span>{' '}
                  <Link to={`/states/${war.state.id}`} className="text-neonBlue hover:text-blue-400">
                    {war.state.name}
                  </Link>
                </p>
              )}
              <p className="mb-2 text-gray-300">
                <span className="font-medium text-white">Started:</span> {formatDate(war.declaredAt)}
              </p>
              {war.endedAt && (
                <p className="mb-2 text-gray-300">
                  <span className="font-medium text-white">Ended:</span> {formatDate(war.endedAt)}
                </p>
              )}
            </div>


            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4 bg-gray-700 p-4 rounded-md">
                {/* Attacking Region */}
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Attacking Region</h3>
                  {war.attackerRegion ? (
                    <div>
                      <p className="text-gray-300 mb-1">
                        <Link to={`/regions/${war.attackerRegion.id}`} className="text-neonBlue hover:text-blue-400">
                          {war.attackerRegion.name}
                        </Link>
                      </p>
                      {war.attackerState && (
                        <p className="text-gray-300 mb-1">
                          <span className="text-gray-400">State: </span>
                          <Link to={`/states/${war.attackerState.id}`} className="text-neonBlue hover:text-blue-400">
                            {war.attackerState.name}
                          </Link>
                        </p>
                      )}
                      {/* {war.attackerRegion.resources && (
                        <p className="text-gray-300 text-sm">
                          <span className="text-gray-400">Resources: </span>
                          {Object.entries(war.attackerRegion.resources)
                            .filter(([_, value]) => value && value.max > 0)
                            .map(([key]) => key)
                            .join(', ')}
                        </p>
                      )} */}
                    </div>
                  ) : (
                    <p className="text-gray-400">No information available</p>
                  )}
                </div>

                {/* Defending Region */}
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Defending Region</h3>
                  {war.defenderRegion ? (
                    <div>
                      <p className="text-gray-300 mb-1">
                        <Link to={`/regions/${war.defenderRegion.id}`} className="text-neonBlue hover:text-blue-400">
                          {war.defenderRegion.name}
                        </Link>
                      </p>
                      {war.defenderState && (
                        <p className="text-gray-300 mb-1">
                          <span className="text-gray-400">State: </span>
                          <Link to={`/states/${war.defenderState.id}`} className="text-neonBlue hover:text-blue-400">
                            {war.defenderState.name}
                          </Link>
                        </p>
                      )}
                      {/* {war.defenderRegion.resources && (
                        <p className="text-gray-300 text-sm">
                          <span className="text-gray-400">Resources: </span>
                          {Object.entries(war.defenderRegion.resources)
                            .filter(([_, value]) => value && value.max > 0)
                            .map(([key]) => key)
                            .join(', ')}
                        </p>
                      )} */}
                    </div>
                  ) : (
                    <p className="text-gray-400">No information available</p>
                  )}
                </div>
              </div>


            <div className="mb-6">
              <h2 className="text-xl font-semibold mb-3 text-white">War Progress</h2>
              <div className="flex justify-between text-sm mb-1 text-gray-300">
                <span>Attackers: {(war.attackerGroundDamage || 0) + (war.attackerSeaDamage || 0)} damage</span>
                <span>Defenders: {(war.defenderGroundDamage || 0) + (war.defenderSeaDamage || 0)  + war.damageRequirement } damage</span>
              </div>
              <div className="w-full h-4 bg-gray-600 rounded-full overflow-hidden">
                <div
                  className="h-full bg-red-600"
                  style={{ width: `${getProgressPercentage()}%` }}
                ></div>
              </div>
              <div className="flex justify-between text-xs mt-1 text-gray-400">
                <span>Attacker Victory</span>
                <span>Defender Victory</span>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div className="bg-gray-700 p-4 rounded-md">
                <h2 className="text-xl font-semibold mb-3 text-white">Attackers ({consolidatedAttackers.length})</h2>
                {consolidatedAttackers.length === 0 ? (
                  <p className="text-gray-400">No attackers yet</p>
                ) : (
                  <div>
                    <ul className="divide-y divide-gray-600">
                      {consolidatedAttackers.slice(0, 5).map(participant => (
                        <li key={participant.id} className="py-2 flex justify-between items-center">
                          <div>
                            {participant.userId ? (
                              <Link to={`/users/${participant.userId}`} className="text-neonBlue hover:text-blue-400">
                                {participant?.username}
                              </Link>
                            ) : participant.state ? (
                              <Link to={`/states/${participant.state.id}`} className="text-neonBlue hover:text-blue-400">
                                {participant.state.name}
                              </Link>
                            ) : (
                              'Unknown participant'
                            )}
                            {participant.count > 1 && (
                              <span className="ml-2 text-xs text-gray-400">
                                ({participant.count} attacks)
                              </span>
                            )}
                          </div>
                          {participant.totalDamage > 0 && (
                            <span className="text-sm text-gray-300 bg-gray-600 px-2 py-1 rounded">
                              {participant.totalDamage.toLocaleString()} damage
                            </span>
                          )}
                        </li>
                      ))}
                    </ul>
                    {consolidatedAttackers.length > 5 && (
                      <div className="mt-3 text-center">
                        <button
                          onClick={() => {
                            setModalSide('attackers');
                            setShowModal(true);
                          }}
                          className="text-neonBlue hover:text-blue-400 text-sm"
                        >
                          View all {consolidatedAttackers.length} attackers...
                        </button>
                      </div>
                    )}
                  </div>
                )}
              </div>

              <div className="bg-gray-700 p-4 rounded-md">
                <h2 className="text-xl font-semibold mb-3 text-white">Defenders ({consolidatedDefenders.length})</h2>
                {consolidatedDefenders.length === 0 ? (
                  <p className="text-gray-400">No defenders yet</p>
                ) : (
                  <div>
                    <ul className="divide-y divide-gray-600">
                      {consolidatedDefenders.slice(0, 5).map(participant => (
                        <li key={participant.id} className="py-2 flex justify-between items-center">
                          <div>
                            {participant.userId ? (
                              <Link to={`/users/${participant.userId}`} className="text-neonBlue hover:text-blue-400">
                                {participant?.username}
                              </Link>
                            ) : participant.state ? (
                              <Link to={`/states/${participant.state.id}`} className="text-neonBlue hover:text-blue-400">
                                {participant.state.name}
                              </Link>
                            ) : (
                              'Unknown participant'
                            )}
                            {participant.count > 1 && (
                              <span className="ml-2 text-xs text-gray-400">
                                ({participant.count} attacks)
                              </span>
                            )}
                          </div>
                          {participant.totalDamage > 0 && (
                            <span className="text-sm text-gray-300 bg-gray-600 px-2 py-1 rounded">
                              {participant.totalDamage.toLocaleString()} damage
                            </span>
                          )}
                        </li>
                      ))}
                    </ul>
                    {consolidatedDefenders.length > 5 && (
                      <div className="mt-3 text-center">
                        <button
                          onClick={() => {
                            setModalSide('defenders');
                            setShowModal(true);
                          }}
                          className="text-neonBlue hover:text-blue-400 text-sm"
                        >
                          View all {consolidatedDefenders.length} defenders...
                        </button>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            {war.battleEvents && war.battleEvents.length > 0 && (
              <div className="mb-6">
                <h2 className="text-xl font-semibold mb-3 text-white">Recent Battle Events</h2>
                <div className="bg-gray-700 rounded-md">
                  <ul className="divide-y divide-gray-600">
                    {war.battleEvents.slice(0, 3).map(event => (
                      <li key={event.id} className="p-3">
                        <div className="flex items-start">
                          <div className="flex-1">
                            <p className="font-medium text-white">{event.title}</p>
                            <p className="text-sm text-gray-300">{event.description}</p>
                            <p className="text-xs text-gray-400 mt-1">
                              {formatDate(event.createdAt)}
                            </p>
                          </div>
                          {event.damage && (
                            <span className="px-2 py-1 bg-red-900 text-red-100 rounded text-xs font-medium">
                              {event.damage} DMG
                            </span>
                          )}
                        </div>
                      </li>
                    ))}
                  </ul>
                  {/* {war.battleEvents.length > 3 && (
                    <div className="p-3 text-center">
                      <button
                        onClick={() => setActiveTab('events')}
                        className="text-neonBlue hover:text-blue-400 text-sm"
                      >
                        View all events
                      </button>
                    </div>
                  )} */}
                </div>
              </div>
            )}

            {isWarActive(war.status) && !participating && (
              <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
                {userData?.region ? (
                  // User has a region - automatically determine which side they can join
                  (() => {
                    // Check if user's region is the attacker or defender region
                    const isAttackerRegion = war.attackerRegion && userData.region.id === war.attackerRegion.id;
                    const isDefenderRegion = war.defenderRegion && userData.region.id === war.defenderRegion.id;

                    if (isAttackerRegion) {
                      return (
                        <button
                          onClick={() => handleJoinWar(true)}
                          disabled={participationLoading}
                          className="px-6 py-3 bg-red-600 rounded-md hover:bg-red-700 disabled:bg-gray-500 disabled:text-gray-300"
                        >
                          Join War as Attacker
                        </button>
                      );
                    } else if (isDefenderRegion) {
                      return (
                        <button
                          onClick={() => handleJoinWar(false)}
                          disabled={participationLoading}
                          className="px-6 py-3 bg-blue-600 rounded-md hover:bg-blue-700 disabled:bg-gray-500 disabled:text-gray-300"
                        >
                          Join War as Defender
                        </button>
                      );
                    } else {
                      // User's region is not directly involved in the war
                      return (
                        <div className="text-center">
                          <p className="text-yellow-400 mb-4">Your region is not directly involved in this war.</p>
                          <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <button
                              onClick={() => handleJoinWar(true)}
                              disabled={participationLoading}
                              className="px-6 py-3 bg-red-600 rounded-md hover:bg-red-700 disabled:bg-gray-500 disabled:text-gray-300"
                            >
                              Join as Attacker
                            </button>
                            <button
                              onClick={() => handleJoinWar(false)}
                              disabled={participationLoading}
                              className="px-6 py-3 bg-blue-600 rounded-md hover:bg-blue-700 disabled:bg-gray-500 disabled:text-gray-300"
                            >
                              Join as Defender
                            </button>
                          </div>
                        </div>
                      );
                    }
                  })()
                ) : (
                  // User doesn't have a region assigned - show both options
                  <>
                    <button
                      onClick={() => handleJoinWar(true)}
                      disabled={participationLoading}
                      className="px-6 py-3 bg-red-600 rounded-md hover:bg-red-700 disabled:bg-gray-500 disabled:text-gray-300"
                    >
                      Join as Attacker
                    </button>
                    <button
                      onClick={() => handleJoinWar(false)}
                      disabled={participationLoading}
                      className="px-6 py-3 bg-blue-600 rounded-md hover:bg-blue-700 disabled:bg-gray-500 disabled:text-gray-300"
                    >
                      Join as Defender
                    </button>
                  </>
                )}
              </div>
            )}

            {participating && (
              <div className="mt-6 p-4 bg-gray-700 border border-gray-600 rounded-md text-center">
                <p className="text-green-400 mb-2">You are already participating in this war.</p>
                {isWarActive(war.status) && (
                  <button
                    onClick={() => setActiveTab('participate')}
                    className="px-4 py-2 bg-blue-600 rounded-md hover:bg-blue-700 text-sm"
                  >
                    Fight
                  </button>
                )}
              </div>
            )}
          </div>
        )}

        {/* Participate Tab */}
        {activeTab === 'participate' && participating && isWarActive(war.status) && (
          <div>
            <div className="mb-6">
              <h2 className="text-xl font-semibold mb-3 text-white">War Participation</h2>
              <p className="text-gray-300 mb-4">
                Use your energy to participate in this war. The more energy you spend, the more damage you'll deal to the enemy.
              </p>

              <div className="bg-gray-700 p-4 rounded-md mb-4">
                <h3 className="font-medium text-white mb-2">Auto Mode</h3>
                <p className="text-gray-300 text-sm">
                  When auto mode is enabled, the system will automatically use your energy for battles based on the percentage you set.
                  This allows you to participate in the war even when you're offline.
                </p>
              </div>
            </div>

            <WarParticipateForm
              warId={id}
              onParticipationComplete={handleParticipationComplete}
            />
          </div>
        )}

        {/* Preparation Tab */}
        {/* {activeTab === 'preparation' && (
          <WarPreparation warId={id} />
        )} */}

        {/* Supplies Tab - Hidden */}
        {/* {activeTab === 'supplies' && (
          <WarSupplies warId={id} war={war} />
        )} */}

        {/* Morale Tab - Hidden */}
        {/* {activeTab === 'morale' && (
          <WarMorale warId={id} />
        )} */}

        {/* Costs Tab - Hidden */}
        {/* {activeTab === 'costs' && (
          <WarCosts warId={id} />
        )} */}

        {/* Events Tab - Hidden */}
        {/* {activeTab === 'events' && (
          <WarEvents warId={id} />
        )} */}

        {/* Region History Tab */}
        {activeTab === 'history' && war?.defenderRegion?.id && (
          <RegionWarHistory regionId={war.defenderRegion.id} />
        )}

        {/* Regional Performance Tab */}
        {activeTab === 'performance' && war?.defenderRegion?.id && (
          <RegionalPerformance regionId={war.defenderRegion.id} />
        )}

        {/* Participants Modal */}
        {showModal && (
          <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
            <div className="bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] flex flex-col">
              <div className="flex justify-between items-center p-4 border-b border-gray-700">
                <h2 className="text-xl font-semibold text-white">
                  {modalSide === 'attackers' ? 'All Attackers' : 'All Defenders'} ({modalSide === 'attackers' ? consolidatedAttackers.length : consolidatedDefenders.length})
                </h2>
                <button
                  onClick={() => setShowModal(false)}
                  className="text-gray-400 hover:text-white"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </div>

              <div className="p-4 overflow-y-auto flex-grow">
                {modalSide === 'attackers' ? (
                  <ul className="divide-y divide-gray-700">
                    {consolidatedAttackers.map(participant => (
                      <li key={participant.userId} className="py-3 flex justify-between items-center">
                        <div>
                          {participant.userId ? (
                            <Link to={`/users/${participant.userId}`} className="text-neonBlue hover:text-blue-400 font-medium">
                              {participant?.username}
                            </Link>
                          ) : participant.state ? (
                            <Link to={`/states/${participant.state.id}`} className="text-neonBlue hover:text-blue-400 font-medium">
                              {participant.state.name}
                            </Link>
                          ) : (
                            'Unknown participant'
                          )}
                          {participant.count > 1 && (
                            <span className="ml-2 text-xs text-gray-400">
                              ({participant.count} attacks)
                            </span>
                          )}
                        </div>
                        {participant.totalDamage > 0 && (
                          <span className="bg-red-900 text-red-100 px-2 py-1 rounded text-sm">
                            {participant.totalDamage.toLocaleString()} damage
                          </span>
                        )}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <ul className="divide-y divide-gray-700">
                    {consolidatedDefenders.map(participant => (
                      <li key={participant.userId} className="py-3 flex justify-between items-center">
                        <div>
                          {participant.userId ? (
                            <Link to={`/users/${participant.userId}`} className="text-neonBlue hover:text-blue-400 font-medium">
                              {participant.username}
                            </Link>
                          ) : participant.state ? (
                            <Link to={`/states/${participant.state.id}`} className="text-neonBlue hover:text-blue-400 font-medium">
                              {participant.state.name}
                            </Link>
                          ) : (
                            'Unknown participant'
                          )}
                          {participant.count > 1 && (
                            <span className="ml-2 text-xs text-gray-400">
                              ({participant.count} attacks)
                            </span>
                          )}
                        </div>
                        {participant.totalDamage > 0 && (
                          <span className="bg-blue-900 text-blue-100 px-2 py-1 rounded text-sm">
                            {participant.totalDamage.toLocaleString()} damage
                          </span>
                        )}
                      </li>
                    ))}
                  </ul>
                )}
              </div>

              <div className="p-4 border-t border-gray-700">
                <button
                  onClick={() => setShowModal(false)}
                  className="w-full bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-md"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default WarDetail;