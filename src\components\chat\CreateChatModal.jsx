import React, { useState, useEffect, useMemo } from 'react';
import { X, Search, Users, User, Plus, Check } from 'lucide-react';
import { chatService } from '../../services/api/chat.service.js';
import { CHAT_CONFIG, validateChatName, validateParticipants } from '../../config/chat.config.js';
import { showErrorToast } from '../../utils/showErrorToast.js';
import { showSuccessToast } from '../../utils/showSuccessToast.js';
import useAuthStore from '../../store/useAuthStore.js';

/**
 * Create chat modal component
 * Allows users to create new direct or group chats
 */
const CreateChatModal = ({ isOpen, onClose, onChatCreated }) => {
  const { user } = useAuthStore();
  const [chatType, setChatType] = useState('direct');
  const [chatName, setChatName] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [errors, setErrors] = useState({});

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setChatType('direct');
      setChatName('');
      setSearchQuery('');
      setSearchResults([]);
      setSelectedUsers([]);
      setErrors({});
    }
  }, [isOpen]);

  // Debounced user search
  useEffect(() => {
    if (!searchQuery.trim() || searchQuery.length < 2) {
      setSearchResults([]);
      return;
    }

    const timeoutId = setTimeout(async () => {
      setIsSearching(true);
      try {
        const results = await chatService.searchUsers(searchQuery, 10);
        // Filter out current user and already selected users
        const filteredResults = results.filter(
          u => u.id !== user?.id && !selectedUsers.find(s => s.id === u.id)
        );
        setSearchResults(filteredResults);
      } catch (error) {
        console.error('Failed to search users:', error);
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    }, CHAT_CONFIG.PERFORMANCE.DEBOUNCE_SEARCH);

    return () => clearTimeout(timeoutId);
  }, [searchQuery, selectedUsers, user?.id]);

  const handleUserSelect = (selectedUser) => {
    if (chatType === 'direct') {
      // For direct chats, only allow one user
      setSelectedUsers([selectedUser]);
      setSearchQuery('');
      setSearchResults([]);
    } else {
      // For group chats, allow multiple users
      if (!selectedUsers.find(u => u.id === selectedUser.id)) {
        setSelectedUsers([...selectedUsers, selectedUser]);
        setSearchQuery('');
        setSearchResults([]);
      }
    }
    setErrors({ ...errors, participants: null });
  };

  const handleUserRemove = (userId) => {
    setSelectedUsers(selectedUsers.filter(u => u.id !== userId));
  };

  const handleChatTypeChange = (type) => {
    setChatType(type);
    setSelectedUsers([]);
    setChatName('');
    setErrors({});
  };

  const validateForm = () => {
    const newErrors = {};

    // Validate participants
    const participantsValidation = validateParticipants(selectedUsers.map(u => u.id));
    if (!participantsValidation.valid) {
      newErrors.participants = participantsValidation.error;
    }

    // Validate chat name for groups
    if (chatType === 'group') {
      const nameValidation = validateChatName(chatName, true);
      if (!nameValidation.valid) {
        newErrors.name = nameValidation.error;
      }
    }

    // Direct chat specific validation
    if (chatType === 'direct' && selectedUsers.length !== 1) {
      newErrors.participants = 'Please select exactly one user for direct chat';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCreateChat = async () => {
    if (!validateForm()) return;

    setIsCreating(true);
    try {
      const chatData = {
        type: chatType,
        participantIds: selectedUsers.map(u => u.id),
        ...(chatType === 'group' && chatName.trim() && { name: chatName.trim() }),
      };

      const newChat = await chatService.createChat(chatData);
      showSuccessToast(CHAT_CONFIG.SUCCESS.CHAT_CREATED);
      onChatCreated(newChat);
    } catch (error) {
      console.error('Failed to create chat:', error);
      showErrorToast(error.message || 'Failed to create chat');
    } finally {
      setIsCreating(false);
    }
  };

  const filteredSearchResults = useMemo(() => {
    return searchResults.filter(user => 
      !selectedUsers.find(selected => selected.id === user.id)
    );
  }, [searchResults, selectedUsers]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-lg shadow-2xl max-w-md w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <h2 className="text-xl font-bold text-white flex items-center gap-2">
            <Plus className="w-6 h-6 text-neonBlue" />
            Create New Chat
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-800 rounded-lg"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Chat Type Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-3">
              Chat Type
            </label>
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={() => handleChatTypeChange('direct')}
                className={`
                  p-4 rounded-lg border-2 transition-all duration-200 text-left
                  ${chatType === 'direct'
                    ? 'border-neonBlue bg-neonBlue/10 text-white'
                    : 'border-gray-600 bg-gray-800 text-gray-300 hover:border-gray-500'
                  }
                `}
              >
                <User className="w-6 h-6 mb-2" />
                <div className="font-medium">Direct Chat</div>
                <div className="text-sm text-gray-400">One-on-one conversation</div>
              </button>

              <button
                onClick={() => handleChatTypeChange('group')}
                className={`
                  p-4 rounded-lg border-2 transition-all duration-200 text-left
                  ${chatType === 'group'
                    ? 'border-neonBlue bg-neonBlue/10 text-white'
                    : 'border-gray-600 bg-gray-800 text-gray-300 hover:border-gray-500'
                  }
                `}
              >
                <Users className="w-6 h-6 mb-2" />
                <div className="font-medium">Group Chat</div>
                <div className="text-sm text-gray-400">Multiple participants</div>
              </button>
            </div>
          </div>

          {/* Group Chat Name */}
          {chatType === 'group' && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Group Name *
              </label>
              <input
                type="text"
                value={chatName}
                onChange={(e) => {
                  setChatName(e.target.value);
                  setErrors({ ...errors, name: null });
                }}
                placeholder="Enter group name"
                maxLength={CHAT_CONFIG.CHATS.MAX_NAME_LENGTH}
                className={`
                  w-full px-3 py-2 bg-gray-800 border rounded-lg text-white placeholder-gray-400
                  focus:outline-none focus:ring-2 focus:ring-neonBlue focus:border-transparent
                  ${errors.name ? 'border-red-500' : 'border-gray-600'}
                `}
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-400">{errors.name}</p>
              )}
              <p className="mt-1 text-xs text-gray-400">
                {chatName.length}/{CHAT_CONFIG.CHATS.MAX_NAME_LENGTH} characters
              </p>
            </div>
          )}

          {/* User Search */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {chatType === 'direct' ? 'Select User' : 'Add Participants'} *
            </label>
            
            {/* Search Input */}
            <div className="relative mb-3">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search users by username..."
                className="w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-neonBlue focus:border-transparent"
              />
              {isSearching && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <div className="animate-spin w-4 h-4 border-2 border-neonBlue border-t-transparent rounded-full"></div>
                </div>
              )}
            </div>

            {/* Selected Users */}
            {selectedUsers.length > 0 && (
              <div className="mb-3">
                <div className="text-sm text-gray-400 mb-2">Selected:</div>
                <div className="flex flex-wrap gap-2">
                  {selectedUsers.map(user => (
                    <div
                      key={user.id}
                      className="flex items-center gap-2 bg-neonBlue/20 text-neonBlue px-3 py-1 rounded-full text-sm"
                    >
                      <span>{user.username}</span>
                      <button
                        onClick={() => handleUserRemove(user.id)}
                        className="hover:text-white transition-colors"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Search Results */}
            {filteredSearchResults.length > 0 && (
              <div className="border border-gray-600 rounded-lg max-h-48 overflow-y-auto">
                {filteredSearchResults.map(user => (
                  <button
                    key={user.id}
                    onClick={() => handleUserSelect(user)}
                    className="w-full p-3 text-left hover:bg-gray-800 transition-colors border-b border-gray-700 last:border-b-0 flex items-center justify-between"
                  >
                    <div>
                      <div className="font-medium text-white">{user.username}</div>
                      <div className="text-sm text-gray-400">Level {user.level}</div>
                    </div>
                    <Plus className="w-4 h-4 text-gray-400" />
                  </button>
                ))}
              </div>
            )}

            {/* No Results */}
            {searchQuery.length >= 2 && !isSearching && filteredSearchResults.length === 0 && (
              <div className="text-center py-4 text-gray-400 text-sm">
                No users found matching "{searchQuery}"
              </div>
            )}

            {errors.participants && (
              <p className="mt-2 text-sm text-red-400">{errors.participants}</p>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleCreateChat}
            disabled={isCreating || selectedUsers.length === 0}
            className={`
              px-6 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2
              ${isCreating || selectedUsers.length === 0
                ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                : 'bg-neonBlue hover:bg-blue-600 text-white'
              }
            `}
          >
            {isCreating ? (
              <>
                <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                Creating...
              </>
            ) : (
              <>
                <Check className="w-4 h-4" />
                Create Chat
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CreateChatModal;
