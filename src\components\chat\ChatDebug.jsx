import React, { useEffect, useState } from 'react';
import useChatStore from '../../store/useChatStore.js';
import useAuthStore from '../../store/useAuthStore.js';

/**
 * Debug component to help troubleshoot chat connection issues
 * Add this temporarily to any page to see connection status
 */
const ChatDebug = () => {
  const { user, access_token } = useAuthStore();
  const {
    isConnected,
    isConnecting,
    connectionError,
    connect,
    disconnect,
    getConnectionStatus,
  } = useChatStore();

  const [status, setStatus] = useState(null);

  useEffect(() => {
    const updateStatus = () => {
      setStatus(getConnectionStatus());
    };

    updateStatus();
    const interval = setInterval(updateStatus, 1000);

    return () => clearInterval(interval);
  }, [getConnectionStatus]);

  const handleTestConnection = () => {
    console.log('=== CHAT DEBUG TEST ===');
    console.log('User:', user);
    console.log('Access Token:', access_token ? 'Present' : 'Missing');
    console.log('API Base URL:', import.meta.env.VITE_API_BASE_URL);
    console.log('Connection Status:', status);
    console.log('========================');
    
    if (!isConnected && !isConnecting) {
      connect();
    }
  };

  const handleDisconnect = () => {
    disconnect();
  };

  return (
    <div className="fixed bottom-4 right-4 bg-gray-800 text-white p-4 rounded-lg shadow-lg max-w-sm z-50">
      <h3 className="text-lg font-bold mb-2">Chat Debug</h3>
      
      <div className="space-y-2 text-sm">
        <div>
          <strong>User:</strong> {user?.username || 'Not logged in'}
        </div>
        <div>
          <strong>Token:</strong> {access_token ? '✅ Present' : '❌ Missing'}
        </div>
        <div>
          <strong>API URL:</strong> {import.meta.env.VITE_API_BASE_URL || 'Not set'}
        </div>
        <div>
          <strong>Connected:</strong> {isConnected ? '✅ Yes' : '❌ No'}
        </div>
        <div>
          <strong>Connecting:</strong> {isConnecting ? '🔄 Yes' : '❌ No'}
        </div>
        {connectionError && (
          <div className="text-red-400">
            <strong>Error:</strong> {connectionError}
          </div>
        )}
        {status && (
          <div>
            <strong>Status:</strong>
            <pre className="text-xs mt-1 bg-gray-700 p-2 rounded">
              {JSON.stringify(status, null, 2)}
            </pre>
          </div>
        )}
      </div>

      <div className="flex gap-2 mt-4">
        <button
          onClick={handleTestConnection}
          disabled={isConnecting}
          className="px-3 py-1 bg-blue-600 text-white rounded text-sm disabled:opacity-50"
        >
          Test Connect
        </button>
        <button
          onClick={handleDisconnect}
          disabled={!isConnected}
          className="px-3 py-1 bg-red-600 text-white rounded text-sm disabled:opacity-50"
        >
          Disconnect
        </button>
      </div>
    </div>
  );
};

export default ChatDebug;
