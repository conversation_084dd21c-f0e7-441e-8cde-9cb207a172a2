import { io } from 'socket.io-client';
import { CHAT_CONFIG } from '../../config/chat.config.js';

/**
 * WebSocket service for real-time chat functionality
 * Handles connection, message sending, and event management
 */
class ChatWebSocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    this.heartbeatInterval = null;
    this.eventHandlers = new Map();
    this.messageQueue = [];
    this.rateLimitTracker = {
      messages: [],
      typing: [],
    };
    this.connectionCount = 0; // Track connection attempts
    this.lastConnectionAttempt = 0; // Prevent rapid reconnection attempts
    this.currentToken = null; // Track current auth token
  }

  /**
   * Connect to the WebSocket server
   * @param {string} token - JWT authentication token
   */
  connect(token) {
    // Check if already connected with the same token
    if (this.isConnectedWithToken(token)) {
      if (CHAT_CONFIG.DEV.ENABLE_DEBUG_LOGS) {
        console.log('ChatWebSocket: Already connected with this token');
      }
      return;
    }

    // Prevent multiple connections
    if (this.isConnected || this.isConnecting) {
      if (CHAT_CONFIG.DEV.ENABLE_DEBUG_LOGS) {
        console.log('ChatWebSocket: Connection already exists or in progress');
      }
      return;
    }

    // Prevent rapid reconnection attempts
    const now = Date.now();
    const timeSinceLastAttempt = now - this.lastConnectionAttempt;
    if (timeSinceLastAttempt < CHAT_CONFIG.WEBSOCKET.RECONNECTION_DELAY) {
      if (CHAT_CONFIG.DEV.ENABLE_DEBUG_LOGS) {
        console.log('ChatWebSocket: Too soon for reconnection attempt');
      }
      return;
    }

    // Check connection count limit
    if (this.connectionCount >= CHAT_CONFIG.WEBSOCKET.MAX_CONCURRENT_CONNECTIONS) {
      console.warn('ChatWebSocket: Maximum concurrent connections reached');
      return;
    }

    // Validate token
    if (!token) {
      console.error('ChatWebSocket: No authentication token provided');
      return;
    }

    this.isConnecting = true;
    this.lastConnectionAttempt = now;
    this.currentToken = token;

    try {
      const socketUrl = CHAT_CONFIG.API.BASE_URL;

      if (CHAT_CONFIG.DEV.ENABLE_DEBUG_LOGS) {
        console.log('ChatWebSocket: Attempting to connect to:', socketUrl + CHAT_CONFIG.WEBSOCKET.NAMESPACE);
        console.log('ChatWebSocket: Using token:', token ? 'present' : 'missing');
      }

      this.socket = io(socketUrl + CHAT_CONFIG.WEBSOCKET.NAMESPACE, {
        auth: { token }, // No 'Bearer' prefix as per documentation
        autoConnect: false, // We'll manually connect
        reconnection: CHAT_CONFIG.WEBSOCKET.RECONNECTION,
        reconnectionAttempts: CHAT_CONFIG.WEBSOCKET.RECONNECTION_ATTEMPTS,
        reconnectionDelay: CHAT_CONFIG.WEBSOCKET.RECONNECTION_DELAY,
        reconnectionDelayMax: CHAT_CONFIG.WEBSOCKET.RECONNECTION_DELAY_MAX,
        timeout: CHAT_CONFIG.WEBSOCKET.TIMEOUT,
      });

      this.setupEventListeners();

      // Manually connect the socket
      this.socket.connect();

      // Set a timeout to handle connection failures
      const connectionTimeout = setTimeout(() => {
        if (this.isConnecting && !this.isConnected) {
          console.error('ChatWebSocket: Connection timeout');
          this.handleConnectionError(new Error('Connection timeout'));
        }
      }, CHAT_CONFIG.WEBSOCKET.TIMEOUT);

      // Clear timeout when connected
      this.socket.once('connect', () => {
        clearTimeout(connectionTimeout);
      });

      if (CHAT_CONFIG.DEV.ENABLE_DEBUG_LOGS) {
        console.log('ChatWebSocket: Connection initiated');
      }
    } catch (error) {
      console.error('ChatWebSocket: Failed to initialize connection:', error);
      this.isConnecting = false;
      this.handleConnectionError(error);
    }
  }

  /**
   * Disconnect from the WebSocket server
   */
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }

    this.isConnected = false;
    this.isConnecting = false;
    this.connectionCount = 0; // Reset connection count
    this.currentToken = null; // Clear token
    this.stopHeartbeat();
    this.clearRateLimitTrackers();

    if (CHAT_CONFIG.DEV.ENABLE_DEBUG_LOGS) {
      console.log('ChatWebSocket: Disconnected');
    }
  }

  /**
   * Setup event listeners for the socket
   */
  setupEventListeners() {
    if (!this.socket) return;

    // Connection events
    this.socket.on(CHAT_CONFIG.EVENTS.CONNECT, this.handleConnect);
    this.socket.on(CHAT_CONFIG.EVENTS.DISCONNECT, this.handleDisconnect);
    this.socket.on(CHAT_CONFIG.EVENTS.CONNECT_ERROR, this.handleConnectionError);
    this.socket.on(CHAT_CONFIG.EVENTS.RECONNECT, this.handleReconnect);
    this.socket.on(CHAT_CONFIG.EVENTS.RECONNECT_ERROR, this.handleReconnectError);
    this.socket.on(CHAT_CONFIG.EVENTS.RECONNECT_FAILED, this.handleReconnectFailed);

    // Chat events
    this.socket.on(CHAT_CONFIG.EVENTS.CONNECTED, this.handleConnected);
    this.socket.on(CHAT_CONFIG.EVENTS.NEW_MESSAGE, this.handleNewMessage);
    this.socket.on(CHAT_CONFIG.EVENTS.MESSAGES_READ, this.handleMessagesRead);
    this.socket.on(CHAT_CONFIG.EVENTS.USER_TYPING, this.handleUserTyping);
    this.socket.on(CHAT_CONFIG.EVENTS.PONG, this.handlePong);
    this.socket.on(CHAT_CONFIG.EVENTS.ERROR, this.handleError);
  }

  /**
   * Event handler properties (arrow functions to maintain 'this' context)
   */
  handleConnect = () => {
    this.isConnected = true;
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    this.connectionCount = 1; // Track active connection
    this.startHeartbeat();
    this.processMessageQueue();

    if (CHAT_CONFIG.DEV.ENABLE_DEBUG_LOGS) {
      console.log('ChatWebSocket: Connected successfully');
    }

    this.emit('connection_status_changed', {
      status: 'connected',
      isConnected: true
    });
  };

  handleDisconnect = (reason) => {
    this.isConnected = false;
    this.connectionCount = 0; // Reset connection count on disconnect
    this.stopHeartbeat();

    if (CHAT_CONFIG.DEV.ENABLE_DEBUG_LOGS) {
      console.log('ChatWebSocket: Disconnected:', reason);
    }

    this.emit('connection_status_changed', {
      status: 'disconnected',
      isConnected: false,
      reason
    });
  };

  handleConnectionError = (error) => {
    this.isConnecting = false;

    console.error('ChatWebSocket: Connection error:', error);

    this.emit('connection_status_changed', {
      status: 'error',
      isConnected: false,
      error: error.message || 'Connection failed'
    });
  };

  handleReconnect = (attemptNumber) => {
    this.reconnectAttempts = attemptNumber;

    if (CHAT_CONFIG.DEV.ENABLE_DEBUG_LOGS) {
      console.log(`ChatWebSocket: Reconnected after ${attemptNumber} attempts`);
    }
  };

  handleReconnectError = (error) => {
    console.error('ChatWebSocket: Reconnection error:', error);
  };

  handleReconnectFailed = () => {
    console.error('ChatWebSocket: Failed to reconnect after maximum attempts');
    this.emit('connection_status_changed', {
      status: 'failed',
      isConnected: false,
      error: 'Failed to reconnect'
    });
  };

  handleConnected = (data) => {
    if (CHAT_CONFIG.DEV.ENABLE_DEBUG_LOGS) {
      console.log('ChatWebSocket: Server confirmed connection:', data);
    }

    this.emit('connected', data);
  };

  handleNewMessage = (data) => {
    if (CHAT_CONFIG.DEV.ENABLE_DEBUG_LOGS) {
      console.log('ChatWebSocket: New message received:', data);
    }

    this.emit('new_message', data);
  };

  handleMessagesRead = (data) => {
    if (CHAT_CONFIG.DEV.ENABLE_DEBUG_LOGS) {
      console.log('ChatWebSocket: Messages read:', data);
    }

    this.emit('messages_read', data);
  };

  handleUserTyping = (data) => {
    if (CHAT_CONFIG.DEV.ENABLE_DEBUG_LOGS) {
      console.log('ChatWebSocket: User typing:', data);
    }

    this.emit('user_typing', data);
  };

  handlePong = (data) => {
    if (CHAT_CONFIG.DEV.ENABLE_DEBUG_LOGS) {
      console.log('ChatWebSocket: Pong received:', data);
    }

    this.emit('pong', data);
  };

  handleError = (error) => {
    console.error('ChatWebSocket: Server error:', error);
    this.emit('error', error);
  };

  /**
   * Send a message through WebSocket
   * @param {string} chatId - Chat ID
   * @param {Object} message - Message object
   */
  sendMessage(chatId, message) {
    if (!this.checkRateLimit('messages')) {
      throw new Error(CHAT_CONFIG.ERRORS.RATE_LIMITED);
    }

    const payload = { chatId, message };

    if (this.isConnected) {
      this.socket.emit(CHAT_CONFIG.EVENTS.SEND_MESSAGE, payload);
      this.trackRateLimit('messages');
    } else {
      this.messageQueue.push({ event: CHAT_CONFIG.EVENTS.SEND_MESSAGE, payload });
    }
  }

  /**
   * Mark messages as read
   * @param {string} chatId - Chat ID
   */
  markAsRead(chatId) {
    const payload = { chatId };

    if (this.isConnected) {
      this.socket.emit(CHAT_CONFIG.EVENTS.MARK_READ, payload);
    } else {
      this.messageQueue.push({ event: CHAT_CONFIG.EVENTS.MARK_READ, payload });
    }
  }

  /**
   * Send typing start event
   * @param {string} chatId - Chat ID
   */
  startTyping(chatId) {
    if (!this.checkRateLimit('typing')) {
      return; // Silently ignore if rate limited
    }

    const payload = { chatId };

    if (this.isConnected) {
      this.socket.emit(CHAT_CONFIG.EVENTS.TYPING_START, payload);
      this.trackRateLimit('typing');
    }
  }

  /**
   * Send typing stop event
   * @param {string} chatId - Chat ID
   */
  stopTyping(chatId) {
    const payload = { chatId };

    if (this.isConnected) {
      this.socket.emit(CHAT_CONFIG.EVENTS.TYPING_STOP, payload);
    }
  }

  /**
   * Send ping for heartbeat
   */
  ping() {
    if (this.isConnected) {
      this.socket.emit(CHAT_CONFIG.EVENTS.PING);
    }
  }

  /**
   * Start heartbeat monitoring
   */
  startHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    this.heartbeatInterval = setInterval(() => {
      this.ping();
    }, CHAT_CONFIG.HEALTH.HEARTBEAT_INTERVAL);
  }

  /**
   * Stop heartbeat monitoring
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Process queued messages when connection is restored
   */
  processMessageQueue() {
    while (this.messageQueue.length > 0 && this.isConnected) {
      const { event, payload } = this.messageQueue.shift();
      this.socket.emit(event, payload);
    }
  }

  /**
   * Check rate limiting
   * @param {string} type - Type of action ('messages' or 'typing')
   * @returns {boolean} - Whether action is allowed
   */
  checkRateLimit(type) {
    if (CHAT_CONFIG.DEV.DISABLE_RATE_LIMITING) {
      return true;
    }

    const now = Date.now();
    const tracker = this.rateLimitTracker[type];
    const limit = type === 'messages'
      ? CHAT_CONFIG.RATE_LIMITS.WEBSOCKET_MESSAGES_PER_MINUTE
      : CHAT_CONFIG.RATE_LIMITS.TYPING_EVENTS_PER_MINUTE;

    // Remove old entries (older than 1 minute)
    const oneMinuteAgo = now - 60000;
    this.rateLimitTracker[type] = tracker.filter(timestamp => timestamp > oneMinuteAgo);

    return this.rateLimitTracker[type].length < limit;
  }

  /**
   * Track rate limit usage
   * @param {string} type - Type of action
   */
  trackRateLimit(type) {
    this.rateLimitTracker[type].push(Date.now());
  }

  /**
   * Clear rate limit trackers
   */
  clearRateLimitTrackers() {
    this.rateLimitTracker.messages = [];
    this.rateLimitTracker.typing = [];
  }

  /**
   * Register event handler
   * @param {string} event - Event name
   * @param {Function} handler - Event handler function
   */
  on(event, handler) {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event).push(handler);
  }

  /**
   * Unregister event handler
   * @param {string} event - Event name
   * @param {Function} handler - Event handler function
   */
  off(event, handler) {
    if (this.eventHandlers.has(event)) {
      const handlers = this.eventHandlers.get(event);
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * Emit event to registered handlers
   * @param {string} event - Event name
   * @param {*} data - Event data
   */
  emit(event, data) {
    if (this.eventHandlers.has(event)) {
      this.eventHandlers.get(event).forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`Error in event handler for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Get connection status
   * @returns {Object} Connection status information
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      reconnectAttempts: this.reconnectAttempts,
      queuedMessages: this.messageQueue.length,
      connectionCount: this.connectionCount,
      currentToken: this.currentToken ? 'present' : 'missing',
    };
  }

  /**
   * Check if already connected with the same token
   * @param {string} token - JWT token to check
   * @returns {boolean} Whether already connected with this token
   */
  isConnectedWithToken(token) {
    return this.isConnected && this.currentToken === token;
  }

  /**
   * Force disconnect and cleanup (for emergency situations)
   */
  forceDisconnect() {
    if (this.socket) {
      this.socket.removeAllListeners();
      this.socket.disconnect();
      this.socket = null;
    }

    this.isConnected = false;
    this.isConnecting = false;
    this.connectionCount = 0;
    this.currentToken = null;
    this.reconnectAttempts = 0;
    this.stopHeartbeat();
    this.clearRateLimitTrackers();
    this.messageQueue = [];

    console.log('ChatWebSocket: Force disconnected and cleaned up');
  }


}

// Create and export singleton instance
export const chatWebSocketService = new ChatWebSocketService();
export default chatWebSocketService;
