import React, { useEffect } from 'react';
import useChatStore from '../../store/useChatStore.js';
import useAuthStore from '../../store/useAuthStore.js';

/**
 * Simple test component to verify chat store functionality
 * This can be temporarily added to any page for testing
 */
const ChatTest = () => {
  const { user } = useAuthStore();
  const {
    isConnected,
    isConnecting,
    connectionError,
    chats,
    totalUnreadCount,
    connect,
    disconnect,
    fetchChats,
    getConnectionStatus,
  } = useChatStore();

  useEffect(() => {
    console.log('ChatTest: Component mounted');
    console.log('ChatTest: User:', user);
    console.log('ChatTest: Connection status:', { isConnected, isConnecting, connectionError });
    console.log('ChatTest: Chats:', chats);
    console.log('ChatTest: Total unread:', totalUnreadCount);
  }, [user, isConnected, isConnecting, connectionError, chats, totalUnreadCount]);

  const handleConnect = () => {
    console.log('ChatTest: Attempting to connect...');
    connect();
  };

  const handleDisconnect = () => {
    console.log('ChatTest: Disconnecting...');
    disconnect();
  };

  const handleFetchChats = async () => {
    console.log('ChatTest: Fetching chats...');
    try {
      await fetchChats();
      console.log('ChatTest: Chats fetched successfully');
    } catch (error) {
      console.error('ChatTest: Failed to fetch chats:', error);
    }
  };

  return (
    <div className="bg-gray-800 p-4 rounded-lg m-4">
      <h3 className="text-white text-lg font-bold mb-4">Chat System Test</h3>

      <div className="space-y-2 text-sm">
        <div className="text-gray-300">
          <strong>User:</strong> {user ? `${user.username} (ID: ${user.id})` : 'Not logged in'}
        </div>

        <div className="text-gray-300">
          <strong>Connection Status:</strong>{' '}
          <span className={`font-medium ${
            isConnected ? 'text-green-400' :
            isConnecting ? 'text-yellow-400' :
            'text-red-400'
          }`}>
            {isConnected ? 'Connected' :
             isConnecting ? 'Connecting...' :
             'Disconnected'}
          </span>
        </div>

        {connectionError && (
          <div className="text-red-400">
            <strong>Error:</strong> {connectionError}
          </div>
        )}

        <div className="text-gray-300">
          <strong>Chats:</strong> {chats.length}
        </div>

        <div className="text-gray-300">
          <strong>Unread Messages:</strong> {totalUnreadCount}
        </div>
      </div>

      <div className="flex gap-2 mt-4">
        <button
          onClick={handleConnect}
          disabled={isConnected || isConnecting}
          className="px-3 py-1 bg-green-600 text-white rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Connect
        </button>

        <button
          onClick={handleDisconnect}
          disabled={!isConnected}
          className="px-3 py-1 bg-red-600 text-white rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Disconnect
        </button>

        <button
          onClick={handleFetchChats}
          className="px-3 py-1 bg-blue-600 text-white rounded text-sm"
        >
          Fetch Chats
        </button>

        <button
          onClick={() => console.log('Connection Status:', getConnectionStatus())}
          className="px-3 py-1 bg-purple-600 text-white rounded text-sm"
        >
          Log Status
        </button>
      </div>

      {chats.length > 0 && (
        <div className="mt-4">
          <h4 className="text-white font-medium mb-2">Chats:</h4>
          <div className="space-y-1 max-h-32 overflow-y-auto">
            {chats.map(chat => (
              <div key={chat.id} className="text-gray-300 text-sm p-2 bg-gray-700 rounded">
                <div className="font-medium">
                  {chat.name || (chat.type === 'direct'
                    ? chat.participants.find(p => p.id !== user?.id)?.username || 'Unknown'
                    : 'Group Chat'
                  )}
                </div>
                <div className="text-xs text-gray-400">
                  Type: {chat.type} | Participants: {chat.participants.length} | Unread: {chat.unreadCount}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatTest;
