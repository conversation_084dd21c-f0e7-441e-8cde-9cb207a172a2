import { create } from "zustand";

const useAuthStore = create((set) => ({
    user: JSON.parse(localStorage.getItem("user")) || null,
  access_token: localStorage.getItem("access_token") || null,
  
  login: (user, access_token) => {    
    localStorage.setItem("access_token", access_token);
    localStorage.setItem("user", JSON.stringify(user));
    set({ user: user, access_token });
  },

  logout: () => {
    localStorage.removeItem("access_token");
    localStorage.removeItem("user");
    set({ user: null, access_token: null });
  },
}));

export default useAuthStore;
